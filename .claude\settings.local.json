{"permissions": {"allow": ["Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(ls:*)", "Bash(find:*)", "Bash(grep:*)", "Bash(cp:*)", "Bash(redis-cli:*)", "Bash(rg:*)", "<PERSON><PERSON>(find /mnt/d/personal/Desktop/hebing -name \"*.toml\" -o -name \"*.json\" -o -name \"*.yaml\" -o -name \"*.yml\")", "Bash(git add newcopy_v2/src/hotpath/redis_subscriber.rs)", "Bash(git commit -m \"$(cat <<''EOF''\n简化BONK协议日志输出，保持与pump协议一致的风格\n\n- 移除详细的调试信息和步骤日志\n- 保留关键的延迟统计信息\n- 简化构建和发送过程的日志输出\n- 统一错误和状态消息格式\n\n🤖 Generated with [<PERSON> Code](https://claude.ai/code)\n\nCo-Authored-By: <PERSON> <<EMAIL>>\nEOF\n)\")", "<PERSON><PERSON>(explorer \"https://solscan.io/tx/4kp5yC18kDn1xPLhyrsoj13Lhvww9Q9kxjEXiF6u4ekZrBnCAwzvCLK7eaPtGgMmCrzoqGcKvdTfpqx1WnuFAmBH\")", "WebFetch(domain:github.com)", "WebFetch(domain:docs.jito.wtf)", "WebFetch(domain:medium.com)", "WebFetch(domain:docs.rs)", "Bash(cd \"/mnt/d/personal/Desktop/hebing/qingqikaifaxxxxxxxxxxxxxxxxx/copybot-fix-price-debug-log/newcopy_v2\")", "Bash(cargo check)"], "deny": []}}