use std::{sync::Arc, time::{Duration, Instant}, str::FromStr};

use anyhow::Result;
use async_trait::async_trait;
use chrono::Local;
use colored::Colorize;
use solana_sdk::{signature::Signature, pubkey::Pubkey};
use tokio::sync::mpsc;
use tracing::{error, info};

use crate::{
    hotpath::{transaction_builder::TransactionBuilder},
    services::{
        transaction_sender::TransactionSender,
        senders::{astralane_sender::AstralaneSender, blockrazor_sender::BlockRazorSender, flashblock_sender::FlashblockSender, oslot_sender::OslotSender},
        transaction_tracker::TrackRequest,
        sol_price_oracle::SolPriceOracle,
        sell_executor_trait::SellExecutorTrait,
    },
    shared::types::{HotPathTrade, TradeType, WalletConfig},
    shared::global_config::get_accelerator_config,
};

#[derive(Clone)]
pub struct SellExecutor {
    tx_builder: Arc<TransactionBuilder>,
    tx_sender: Arc<TransactionSender>,
    tracker_tx: mpsc::Sender<TrackRequest>,
    sol_price_oracle: SolPriceOracle,
}

impl SellExecutor {
    pub fn new(
        tx_builder: Arc<TransactionBuilder>,
        tx_sender: Arc<TransactionSender>,
        tracker_tx: mpsc::Sender<TrackRequest>,
        sol_price_oracle: SolPriceOracle,
    ) -> Self {
        Self {
            tx_builder,
            tx_sender,
            tracker_tx,
            sol_price_oracle,
        }
    }

    pub fn get_wallet_pubkey(&self) -> Pubkey {
        self.tx_builder.get_wallet_pubkey()
    }

    pub async fn execute_sell(
        &self,
        trade: HotPathTrade,
        wallet_config: &WalletConfig,
        token_amount_in: u64,
        decision_dur: Duration,
        reason: &str,
        current_price: f64,
        close_ata: bool,
    ) -> Result<(), anyhow::Error> {
        self.execute_sell_with_slippage(
            trade,
            wallet_config,
            token_amount_in,
            decision_dur,
            reason,
            current_price,
            close_ata,
            None, // 使用默认滑点
        ).await
    }

    /// 执行卖出交易，支持自定义滑点（用于重试）
    pub async fn execute_sell_with_slippage(
        &self,
        trade: HotPathTrade,
        wallet_config: &WalletConfig,
        token_amount_in: u64,
        decision_dur: Duration,
        reason: &str,
        current_price: f64,
        close_ata: bool,
        custom_slippage: Option<f64>, // 自定义滑点，用于重试
    ) -> Result<(), anyhow::Error> {
        let total_start = Instant::now();
        let decimals = 6u32;
        let token_amount_ui = token_amount_in as f64 / 10f64.powi(decimals as i32);
        let expected_sol = token_amount_ui * current_price;

        // 使用自定义滑点或默认滑点
        let slip_pct = custom_slippage.unwrap_or_else(|| {
            wallet_config
                .sell_slippage_percentage
                .unwrap_or(wallet_config.slippage_percentage)
                .max(0.0)
        });

        let min_sol_out = expected_sol * (1.0 - slip_pct / 100.0);
        let mut min_sol_out_lamports = (min_sol_out * 1_000_000_000.0) as u64;
        if min_sol_out_lamports == 0 { min_sol_out_lamports = 1; }

        let accel_cfg = get_accelerator_config();
        
        // 🚀 先获取一个共享的nonce账户（如果启用nonce）
        use crate::services::nonce_helper::is_using_nonce;
        use crate::services::nonce_pool::NonceAccountPool;
        
        let shared_nonce_info = if is_using_nonce() {
            let pool = NonceAccountPool::get_instance();
            if let Some(nonce_info) = pool.get_next_available_account() {
                info!("🎯 获取共享nonce账户用于所有卖出加速器: {} (nonce: {})", nonce_info.address, nonce_info.current_nonce);
                // 立即标记为已使用，避免其他地方重复使用
                pool.release_account(&nonce_info.address);
                Some(nonce_info)
            } else {
                info!("⚠️ 无可用nonce账户，回退到普通交易");
                None
            }
        } else {
            None
        };
        
        // 准备所有可用的加速器 - 使用用户配置的tip计算
        let tip_accounts_and_providers = vec![
            (Pubkey::from_str(AstralaneSender::get_random_tip_account()).ok(), Some("astralane")),
            (Pubkey::from_str(BlockRazorSender::get_random_tip_account()).ok(), Some("blockrazor")),
            (Pubkey::from_str(OslotSender::get_random_tip_account()).ok(), Some("oslot")),
            (Pubkey::from_str(FlashblockSender::get_random_tip_account()).ok(), Some("flashblock")),
        ];

        let mut send_tasks = Vec::new();
        let mut first_signature = None;

        // 使用用户配置的tip百分比并发构建交易
        for (tip_pubkey_opt, accelerator_provider) in tip_accounts_and_providers {
            let build_start = Instant::now();
            if let Ok(unsigned_tx) = self.tx_builder.build_unsigned_sell_transaction_with_nonce(
                &trade, 
                token_amount_in, 
                min_sol_out_lamports, 
                wallet_config, 
                close_ata, 
                tip_pubkey_opt,
                accelerator_provider,
                shared_nonce_info.as_ref(),
            ) {
                let build_dur = build_start.elapsed();
                
                let sign_start = Instant::now();
                if let Ok((signed_tx, signature, _blockhash)) = self.tx_builder.sign_and_log_details(unsigned_tx).await {
                    let sign_finish = Instant::now();
                    let sign_dur = sign_finish.duration_since(sign_start);

                    // 保存第一个签名用于跟踪
                    if first_signature.is_none() {
                        first_signature = Some(signature.clone());
                    }

                    // 创建并发发送任务
                    let sender_clone = self.tx_sender.clone();
                    let provider_name = accelerator_provider.unwrap_or("unknown").to_string();
                    let sig_clone = signature.to_string();
                    
                    let task = tokio::spawn(async move {
                        let send_result = sender_clone.send_transaction(&signed_tx).await;
                        match send_result {
                            Ok(sig) => {
                                info!("✅ 卖出交易发送成功 ({}): {}", provider_name, sig);
                                Ok(sig)
                            }
                            Err(e) => {
                                error!("❌ 卖出交易发送失败 ({}): {} - sig: {}", provider_name, e, sig_clone);
                                Err(e)
                            }
                        }
                    });
                    
                    send_tasks.push(task);
                }
            }
        }
        
        if let Some(signature) = first_signature {
            // 等待所有发送任务完成
            let mut success_count = 0;
            for task in send_tasks {
                match task.await {
                    Ok(Ok(_)) => success_count += 1,
                    _ => {}
                }
            }
            
            info!("📊 所有加速器卖出发送完成，成功: {}个", success_count);
            
            let sol_price_usd = self.sol_price_oracle.get_price_usd();
            let original_token_amount = if trade.token_amount == 0 { 1 } else { trade.token_amount };
            let cost_of_portion_in_sol = trade.sol_cost * (token_amount_in as f64 / original_token_amount as f64);
            let entry_usd = Some(cost_of_portion_in_sol * sol_price_usd);

            let track_req = TrackRequest {
                trade_type: TradeType::Sell, 
                signature,
                mint: trade.mint_pubkey.to_string(), 
                sol_amount: expected_sol,
                token_amount: token_amount_in, 
                user_wallet: self.tx_builder.get_wallet_pubkey().to_string(),
                entry_sol_amount_usd: entry_usd, 
                trade_info: Some(trade.clone()), // 添加trade_info用于重试
                wallet_config: Some(wallet_config.clone()), // 添加wallet_config用于重试
                executor_type: "pump".to_string(), // pump执行器
            };

            if let Err(e) = self.tracker_tx.send(track_req).await {
                error!("无法将卖出交易提交给跟踪服务: {}", e);
            }

            Ok(())
        } else {
            error!("所有加速器卖出交易构建都失败了");
            Err(anyhow::anyhow!("所有加速器卖出交易构建都失败了").into())
        }
    }

    /// 构建并发送卖出交易，返回签名但不创建跟踪记录（用于重试）
    pub async fn build_and_send_sell_transaction(
        &self,
        trade: HotPathTrade,
        wallet_config: &WalletConfig,
        token_amount_in: u64,
        current_price: f64,
        custom_slippage: Option<f64>,
        reason: &str,
    ) -> Result<solana_sdk::signature::Signature, anyhow::Error> {
        let decimals = 6u32;
        let token_amount_ui = token_amount_in as f64 / 10f64.powi(decimals as i32);
        let expected_sol = token_amount_ui * current_price;

        // 使用自定义滑点或默认滑点
        let slip_pct = custom_slippage.unwrap_or_else(|| {
            wallet_config
                .sell_slippage_percentage
                .unwrap_or(wallet_config.slippage_percentage)
                .max(0.0)
        });

        let min_sol_out = expected_sol * (1.0 - slip_pct / 100.0);
        let mut min_sol_out_lamports = (min_sol_out * 1_000_000_000.0) as u64;
        if min_sol_out_lamports == 0 { min_sol_out_lamports = 1; }

        let accel_cfg = get_accelerator_config();
        let tip_pubkey_opt = if accel_cfg.enabled {
            match accel_cfg.provider.as_str() {
                "astralane" => Pubkey::from_str(AstralaneSender::get_random_tip_account()).ok(),
                "blockrazor" => Pubkey::from_str(BlockRazorSender::get_random_tip_account()).ok(),
                "oslot" => Pubkey::from_str(OslotSender::get_random_tip_account()).ok(),
                "flashblock" => Pubkey::from_str(FlashblockSender::get_random_tip_account()).ok(),
                _ => None,
            }
        } else {
            None
        };

        // 构建交易
        let unsigned_tx = self.tx_builder.build_unsigned_sell_transaction(
            &trade, 
            token_amount_in, 
            min_sol_out_lamports, 
            wallet_config, 
            true, 
            tip_pubkey_opt,
            if accel_cfg.enabled { Some(accel_cfg.provider.as_str()) } else { None },
        )?;

        // 签名交易
        let (signed_tx, _signature, _blockhash) = self.tx_builder.sign_and_log_details(unsigned_tx).await?;

        // 发送交易
        match self.tx_sender.send_transaction(&signed_tx).await {
            Ok(sig) => {
                info!("⏳ {} 卖出交易已发送，签名: {}", reason, sig);
                let signature_obj: solana_sdk::signature::Signature = sig.parse()?;
                Ok(signature_obj)
            }
            Err(e) => {
                error!("卖出交易发送失败: {}", e);
                Err(e.into())
            }
        }
    }
}

/// 为SellExecutor实现统一的卖出执行器接口
#[async_trait]
impl SellExecutorTrait for SellExecutor {
    async fn execute_sell(
        &self,
        trade: HotPathTrade,
        wallet_config: &WalletConfig,
        token_amount_in: u64,
        decision_dur: Duration,
        reason: &str,
        current_price: f64,
        close_ata: bool,
    ) -> Result<(), anyhow::Error> {
        self.execute_sell(trade, wallet_config, token_amount_in, decision_dur, reason, current_price, close_ata).await
    }

    async fn execute_sell_with_slippage(
        &self,
        trade: HotPathTrade,
        wallet_config: &WalletConfig,
        token_amount_in: u64,
        decision_dur: Duration,
        reason: &str,
        current_price: f64,
        close_ata: bool,
        custom_slippage: Option<f64>,
    ) -> Result<(), anyhow::Error> {
        self.execute_sell_with_slippage(trade, wallet_config, token_amount_in, decision_dur, reason, current_price, close_ata, custom_slippage).await
    }

    async fn build_and_send_sell_transaction(
        &self,
        trade: HotPathTrade,
        wallet_config: &WalletConfig,
        token_amount_in: u64,
        current_price: f64,
        custom_slippage: Option<f64>,
        reason: &str,
    ) -> Result<Signature, anyhow::Error> {
        self.build_and_send_sell_transaction(trade, wallet_config, token_amount_in, current_price, custom_slippage, reason).await
    }
}

fn fmt_us(d: std::time::Duration) -> String {
    format!("{} µs", d.as_micros())
}