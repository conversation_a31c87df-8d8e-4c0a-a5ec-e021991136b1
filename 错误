PS D:\personal\Desktop\hebing> cd qingqikaifaxxxxxxxxxxxxxxxxx/copybot-fix-price-debug-log/newcopy_v2; $env:RUST_LOG = "info"; cargo run --release
   Compiling newcopy_v2 v0.1.0 (D:\personal\Desktop\hebing\qingqikaifaxxxxxxxxxxxxxxxxx\copybot-fix-price-debug-log\newcopy_v2)
warning: unused import: `Instrument`
 --> src\hotpath\redis_subscriber.rs:3:41
  |
3 | use tracing::{info, debug, warn, error, Instrument};
  |                                         ^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused import: `info`
 --> src\hotpath\bonk_filter.rs:5:15
  |
5 | use tracing::{info, debug};
  |               ^^^^

warning: unused import: `std::str::FromStr`
  --> src\hotpath\transaction_builder.rs:20:5
   |
20 | use std::str::FromStr;
   |     ^^^^^^^^^^^^^^^^^

warning: unused import: `get_transaction_blockhash`
   --> src\hotpath\transaction_builder.rs:419:45
    |
419 | ...   use crate::services::nonce_helper::{get_transaction_blockhash, is_u... 
    |                                           ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `get_transaction_blockhash`
   --> src\hotpath\bonk_builder.rs:273:45
    |
273 | ...   use crate::services::nonce_helper::{get_transaction_blockhash, is_u... 
    |                                           ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `chrono::Local`
 --> src\services\sell_executor.rs:5:5
  |
5 | use chrono::Local;
  |     ^^^^^^^^^^^^^

warning: unused import: `colored::Colorize`
 --> src\services\sell_executor.rs:6:5
  |
6 | use colored::Colorize;
  |     ^^^^^^^^^^^^^^^^^

warning: unused import: `commitment_config::CommitmentConfig`
  --> src\services\bonk_sell_executor.rs:14:5
   |
14 |     commitment_config::CommitmentConfig,
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `debug` and `warn`
  --> src\services\bonk_sell_executor.rs:21:28
   |
21 | use tracing::{error, info, debug, warn};
   |                            ^^^^^  ^^^^

warning: unused import: `get_transaction_blockhash`
   --> src\services\bonk_sell_executor.rs:346:45
    |
346 | ...   use crate::services::nonce_helper::{get_transaction_blockhash, is_u... 
    |                                           ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `uuid`
  --> src\services\senders\blockrazor_sender.rs:12:5
   |
12 | use uuid;
   |     ^^^^

warning: unused import: `tracing::info`
  --> src\services\nonce_scanner.rs:12:5
   |
12 | use tracing::info;
   |     ^^^^^^^^^^^^^

warning: unused import: `Hash`
  --> src\services\nonce_scanner.rs:46:42
   |
46 |             use solana_sdk::hash::{hash, Hash};
   |                                          ^^^^

warning: unused import: `Hash`
  --> src\services\nonce_scanner.rs:78:42
   |
78 |             use solana_sdk::hash::{hash, Hash};
   |                                          ^^^^

warning: unused import: `super::nonce_cache::NonceCache`
  --> src\services\nonce_helper.rs:10:5
   |
10 | use super::nonce_cache::NonceCache;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `error`
 --> src\services\nonce_pool.rs:4:27
  |
4 | use tracing::{info, warn, error};
  |                           ^^^^^

warning: unused imports: `hash::Hash` and `pubkey::Pubkey`
 --> src\services\nonce_pool_manager.rs:2:5
  |
2 |     pubkey::Pubkey,
  |     ^^^^^^^^^^^^^^
3 |     hash::Hash,
  |     ^^^^^^^^^^

warning: unused import: `error`
 --> src\services\nonce_pool_manager.rs:8:27
  |
8 | use tracing::{info, warn, error};
  |                           ^^^^^

warning: unused variable: `signature`
   --> src\hotpath\redis_subscriber.rs:159:25
    |
159 |         let (signed_tx, signature, _blockhash) = match self.bonk_transact... 
    |                         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_signature`
    |
    = note: `#[warn(unused_variables)]` on by default

warning: unused variable: `accel_cfg`
   --> src\hotpath\redis_subscriber.rs:325:45
    |
325 | ...                   let accel_cfg = get_accelerator_config();
    |                           ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_accel_cfg`

warning: unused variable: `accel_cfg`
   --> src\hotpath\redis_subscriber.rs:563:45
    |
563 | ...                   let accel_cfg = get_accelerator_config();
    |                           ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_accel_cfg`

warning: unused variable: `decision_dur`
  --> src\services\sell_executor.rs:79:9
   |
79 |         decision_dur: Duration,
   |         ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_decision_dur`

warning: unused variable: `reason`
  --> src\services\sell_executor.rs:80:9
   |
80 |         reason: &str,
   |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_reason`

warning: unused variable: `total_start`
  --> src\services\sell_executor.rs:85:13
   |
85 |         let total_start = Instant::now();
   |             ^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_total_start`

warning: unused variable: `accel_cfg`
   --> src\services\sell_executor.rs:102:13
    |
102 |         let accel_cfg = get_accelerator_config();
    |             ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_accel_cfg`

warning: unused variable: `build_dur`
   --> src\services\sell_executor.rs:147:21
    |
147 |                 let build_dur = build_start.elapsed();
    |                     ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_build_dur`

warning: unused variable: `sign_dur`
   --> src\services\sell_executor.rs:152:25
    |
152 |                     let sign_dur = sign_finish.duration_since(sign_start);   
    |                         ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_sign_dur`

warning: unused variable: `signature`
   --> src\services\nonce_file_manager.rs:268:13
    |
268 |         let signature = self.rpc_client.send_and_confirm_transaction(&tra... 
    |             ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_signature`

error[E0609]: no field `percentage` on type `&shared::types::WalletConfig`
   --> src\hotpath\bonk_builder.rs:121:55
    |
121 | ...   ((trade.token_amount as f64) * config.percentage / 100.0) as u64       
    |                                             ^^^^^^^^^^ unknown field
    |
    = note: available fields are: `wallet_address`, `is_active`, `remark`, `protocol`, `follow_mode` ... and 42 others

error[E0599]: no method named `build_bonk_buy_instruction` found for reference `&BonkTransactionBuilder` in the current scope
   --> src\hotpath\bonk_builder.rs:157:37
    |
157 |         let main_instruction = self.build_bonk_buy_instruction(
    |                                -----^^^^^^^^^^^^^^^^^^^^^^^^^^ method not found in `&BonkTransactionBuilder`

error[E0599]: no method named `get_min_tip_lamports_for_accelerator` found for reference `&BonkTransactionBuilder` in the current scope
   --> src\hotpath\bonk_builder.rs:170:32
    |
170 | ...n_tip = self.get_min_tip_lamports_for_accelerator(accelerator_provider... 
    |                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ method not found in `&BonkTransactionBuilder`

error[E0609]: no field `fee_config` on type `&BonkTransactionBuilder`
   --> src\hotpath\bonk_builder.rs:181:17
    |
181 |         if self.fee_config.enabled {
    |                 ^^^^^^^^^^ unknown field
    |
    = note: available fields are: `wallet`, `blockhash_service`, `blockhash_lag_slots`, `precomputed_pdas`

error[E0609]: no field `fee_config` on type `&BonkTransactionBuilder`
   --> src\hotpath\bonk_builder.rs:182:53
    |
182 |             if let Some(fee_receiver_pubkey) = self.fee_config.fee_pubkey {  
    |                                                     ^^^^^^^^^^ unknown field 
    |
    = note: available fields are: `wallet`, `blockhash_service`, `blockhash_lag_slots`, `precomputed_pdas`

error[E0609]: no field `fee_config` on type `&BonkTransactionBuilder`
   --> src\hotpath\bonk_builder.rs:183:75
    |
183 | ...amports as f64) * self.fee_config.fee_percentage) as u64;
    |                           ^^^^^^^^^^ unknown field
    |
    = note: available fields are: `wallet`, `blockhash_service`, `blockhash_lag_slots`, `precomputed_pdas`

warning: value assigned to `price_before` is never read
  --> src\protocols\bonk\parser.rs:38:13
   |
38 |     let mut price_before = String::new();
   |             ^^^^^^^^^^^^
   |
   = help: maybe it is overwritten before being read?
   = note: `#[warn(unused_assignments)]` on by default

warning: value assigned to `price_after` is never read
  --> src\protocols\bonk\parser.rs:39:13
   |
39 |     let mut price_after = String::new();
   |             ^^^^^^^^^^^
   |
   = help: maybe it is overwritten before being read?

warning: value assigned to `slippage` is never read
  --> src\protocols\bonk\parser.rs:40:13
   |
40 |     let mut slippage = String::new();
   |             ^^^^^^^^
   |
   = help: maybe it is overwritten before being read?

warning: value assigned to `actual_trade_price` is never read
  --> src\protocols\bonk\parser.rs:41:13
   |
41 |     let mut actual_trade_price = String::new();
   |             ^^^^^^^^^^^^^^^^^^
   |
   = help: maybe it is overwritten before being read?

Some errors have detailed explanations: E0599, E0609.
For more information about an error, try `rustc --explain E0599`.
warning: `newcopy_v2` (bin "newcopy_v2") generated 32 warnings
error: could not compile `newcopy_v2` (bin "newcopy_v2") due to 6 previous errors; 
32 warnings emitted
PS D:\personal\Desktop\hebing\qingqikaifaxxxxxxxxxxxxxxxxx\copybot-fix-price-debug-log\newcopy_v2>