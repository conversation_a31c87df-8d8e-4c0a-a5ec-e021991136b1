PS D:\personal\Desktop\hebing> cd qingqikaifaxxxxxxxxxxxxxxxxx/copybot-fix-price-debug-log/newcopy_v2; $env:RUST_LOG = "info"; cargo run --release
   Compiling newcopy_v2 v0.1.0 (D:\personal\Desktop\hebing\qingqikaifaxxxxxxxxxxxxxxxxx\copybot-fix-price-debug-log\newcopy_v2)
warning: unused import: `Instrument`
 --> src\hotpath\redis_subscriber.rs:3:41
  |
3 | use tracing::{info, debug, warn, error, Instrument};
  |                                         ^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused import: `info`
 --> src\hotpath\bonk_filter.rs:5:15
  |
5 | use tracing::{info, debug};
  |               ^^^^

warning: unused import: `std::str::FromStr`
  --> src\hotpath\transaction_builder.rs:20:5
   |
20 | use std::str::FromStr;
   |     ^^^^^^^^^^^^^^^^^

warning: unused import: `get_transaction_blockhash`
   --> src\hotpath\transaction_builder.rs:293:45
    |
293 | ...   use crate::services::nonce_helper::{get_transaction_blockhash, is_u... 
    |                                           ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `get_transaction_blockhash`
   --> src\hotpath\bonk_builder.rs:163:45
    |
163 | ...   use crate::services::nonce_helper::{get_transaction_blockhash, is_u... 
    |                                           ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `chrono::Local`
 --> src\services\sell_executor.rs:5:5
  |
5 | use chrono::Local;
  |     ^^^^^^^^^^^^^

warning: unused import: `colored::Colorize`
 --> src\services\sell_executor.rs:6:5
  |
6 | use colored::Colorize;
  |     ^^^^^^^^^^^^^^^^^

warning: unused import: `commitment_config::CommitmentConfig`
  --> src\services\bonk_sell_executor.rs:14:5
   |
14 |     commitment_config::CommitmentConfig,
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `debug` and `warn`
  --> src\services\bonk_sell_executor.rs:21:28
   |
21 | use tracing::{error, info, debug, warn};
   |                            ^^^^^  ^^^^

warning: unused import: `get_transaction_blockhash`
   --> src\services\bonk_sell_executor.rs:346:45
    |
346 | ...   use crate::services::nonce_helper::{get_transaction_blockhash, is_u... 
    |                                           ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `uuid`
  --> src\services\senders\blockrazor_sender.rs:12:5
   |
12 | use uuid;
   |     ^^^^

warning: unused import: `tracing::info`
  --> src\services\nonce_scanner.rs:12:5
   |
12 | use tracing::info;
   |     ^^^^^^^^^^^^^

warning: unused import: `Hash`
  --> src\services\nonce_scanner.rs:46:42
   |
46 |             use solana_sdk::hash::{hash, Hash};
   |                                          ^^^^

warning: unused import: `Hash`
  --> src\services\nonce_scanner.rs:78:42
   |
78 |             use solana_sdk::hash::{hash, Hash};
   |                                          ^^^^

warning: unused import: `super::nonce_cache::NonceCache`
  --> src\services\nonce_helper.rs:10:5
   |
10 | use super::nonce_cache::NonceCache;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `error`
 --> src\services\nonce_pool.rs:4:27
  |
4 | use tracing::{info, warn, error};
  |                           ^^^^^

warning: unused imports: `hash::Hash` and `pubkey::Pubkey`
 --> src\services\nonce_pool_manager.rs:2:5
  |
2 |     pubkey::Pubkey,
  |     ^^^^^^^^^^^^^^
3 |     hash::Hash,
  |     ^^^^^^^^^^

warning: unused import: `error`
 --> src\services\nonce_pool_manager.rs:8:27
  |
8 | use tracing::{info, warn, error};
  |                           ^^^^^

warning: unused variable: `signature`
   --> src\hotpath\redis_subscriber.rs:159:25
    |
159 |         let (signed_tx, signature, _blockhash) = match self.bonk_transact... 
    |                         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_signature`
    |
    = note: `#[warn(unused_variables)]` on by default

warning: unused variable: `accel_cfg`
   --> src\hotpath\redis_subscriber.rs:325:45
    |
325 | ...                   let accel_cfg = get_accelerator_config();
    |                           ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_accel_cfg`

warning: unused variable: `accel_cfg`
   --> src\hotpath\redis_subscriber.rs:563:45
    |
563 | ...                   let accel_cfg = get_accelerator_config();
    |                           ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_accel_cfg`

error[E0597]: `trades` does not live long enough
   --> src\hotpath\redis_subscriber.rs:242:30
    |
231 |               let mut trades = if channel == bonk_channel_from_config {      
    |                   ---------- binding `trades` declared here
...
242 |                   for trade in trades.iter_mut() {
    |                                ^^^^^^ borrowed value does not live long enough
...
636 | /                                             tokio::spawn(async move {      
637 | |                                                 // 等待所有发送任务完成，获
取第一个成功的签名
638 | |                                                 let mut success_count = 0; 
639 | |                                                 let mut first_success_s... 
...   |
684 | |                                             });
    | |______________________________________________- argument requires that `trades` is borrowed for `'static`
...
710 |           }
    |           - `trades` dropped here while still borrowed

error[E0597]: `current_filter` does not live long enough
   --> src\hotpath\redis_subscriber.rs:473:43
    |
278 |                       let current_filter = filter.load();
    |                           -------------- binding `current_filter` declared here
...
473 |                       if let Some(config) = current_filter.get_config_if_... 
    |                                             ^^^^^^^^^^^^^^ borrowed value does not live long enough
...
636 | /                                             tokio::spawn(async move {      
637 | |                                                 // 等待所有发送任务完成，获
取第一个成功的签名
638 | |                                                 let mut success_count = 0; 
639 | |                                                 let mut first_success_s... 
...   |
684 | |                                             });
    | |______________________________________________- argument requires that `current_filter` is borrowed for `'static`
...
706 |                   }
    |                   - `current_filter` dropped here while still borrowed       

error[E0521]: borrowed data escapes outside of method
   --> src\hotpath\redis_subscriber.rs:636:45
    |
178 |           &self,
    |           -----
    |           |
    |           `self` is a reference that is only valid in the method body        
    |           let's call the lifetime of this reference `'1`
...
636 | /                                             tokio::spawn(async move {      
637 | |                                                 // 等待所有发送任务完成，获
取第一个成功的签名
638 | |                                                 let mut success_count = 0; 
639 | |                                                 let mut first_success_s... 
...   |
684 | |                                             });
    | |                                              ^
    | |                                              |
    | |______________________________________________`self` escapes the method body here
    |                                                argument requires that `'1` must outlive `'static`

warning: unused variable: `decision_dur`
  --> src\services\sell_executor.rs:79:9
   |
79 |         decision_dur: Duration,
   |         ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_decision_dur`

warning: unused variable: `reason`
  --> src\services\sell_executor.rs:80:9
   |
80 |         reason: &str,
   |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_reason`

warning: unused variable: `total_start`
  --> src\services\sell_executor.rs:85:13
   |
85 |         let total_start = Instant::now();
   |             ^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_total_start`

warning: unused variable: `accel_cfg`
   --> src\services\sell_executor.rs:102:13
    |
102 |         let accel_cfg = get_accelerator_config();
    |             ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_accel_cfg`

warning: unused variable: `build_dur`
   --> src\services\sell_executor.rs:127:21
    |
127 |                 let build_dur = build_start.elapsed();
    |                     ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_build_dur`

warning: unused variable: `sign_dur`
   --> src\services\sell_executor.rs:132:25
    |
132 |                     let sign_dur = sign_finish.duration_since(sign_start);   
    |                         ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_sign_dur`

warning: unused variable: `signature`
   --> src\services\nonce_file_manager.rs:268:13
    |
268 |         let signature = self.rpc_client.send_and_confirm_transaction(&tra... 
    |             ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_signature`

warning: value assigned to `price_before` is never read
  --> src\protocols\bonk\parser.rs:38:13
   |
38 |     let mut price_before = String::new();
   |             ^^^^^^^^^^^^
   |
   = help: maybe it is overwritten before being read?
   = note: `#[warn(unused_assignments)]` on by default

warning: value assigned to `price_after` is never read
  --> src\protocols\bonk\parser.rs:39:13
   |
39 |     let mut price_after = String::new();
   |             ^^^^^^^^^^^
   |
   = help: maybe it is overwritten before being read?

warning: value assigned to `slippage` is never read
  --> src\protocols\bonk\parser.rs:40:13
   |
40 |     let mut slippage = String::new();
   |             ^^^^^^^^
   |
   = help: maybe it is overwritten before being read?

warning: value assigned to `actual_trade_price` is never read
  --> src\protocols\bonk\parser.rs:41:13
   |
41 |     let mut actual_trade_price = String::new();
   |             ^^^^^^^^^^^^^^^^^^
   |
   = help: maybe it is overwritten before being read?

Some errors have detailed explanations: E0521, E0597.
For more information about an error, try `rustc --explain E0521`.
warning: `newcopy_v2` (bin "newcopy_v2") generated 32 warnings
error: could not compile `newcopy_v2` (bin "newcopy_v2") due to 3 previous errors; 
32 warnings emitted
PS D:\personal\Desktop\hebing\qingqikaifaxxxxxxxxxxxxxxxxx\copybot-fix-price-debug-log\newcopy_v2>